import React, { useEffect } from "react";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import { QueryClient, QueryClientProvider } from "react-query";
import { ReactQueryDevtools } from "react-query/devtools";
import { Toaster } from "react-hot-toast";

// Páginas
import NewHomePage from "@/pages/NewHomePage";
import RestaurantPage from "@/pages/RestaurantPage";
import AdminLoginPage from "@/pages/admin/LoginPage";
import AdminLogin from "@/pages/admin/AdminLogin";
import AdminDashboard from "@/pages/admin/Dashboard";
import RestaurantDashboard from "@/components/restaurant/RestaurantDashboard";
import AnalyticsDashboard from "@/pages/AnalyticsDashboard";
import ClientInterface from "@/pages/client/ClientInterface";
import NotFoundPage from "@/pages/NotFoundPage";

// Componentes
import LoadingScreen from "@/components/ui/LoadingScreen";
import NotificationContainer from "@/components/ui/NotificationContainer";
import ProtectedRestaurantRoute from "@/components/auth/ProtectedRestaurantRoute";

// Hooks e serviços
import { useAppStore, initializeStore } from "@/store";
import { wsService } from "@/services/websocket";
import { useAuthInit } from "@/hooks/useAuthInit";

// Estilos
import "./index.css";

// Configuração do React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      staleTime: 5 * 60 * 1000, // 5 minutos
      cacheTime: 10 * 60 * 1000, // 10 minutos
      refetchOnWindowFocus: false,
      refetchOnReconnect: true,
    },
    mutations: {
      retry: 1,
    },
  },
});

// Componente de rota protegida para admin
const ProtectedAdminRoute: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const { isAuthenticated, user } = useAppStore();

  if (!isAuthenticated || !user) {
    return <Navigate to="/admin/login" replace />;
  }

  if (!["admin", "moderator", "super_admin"].includes(user.role)) {
    return <Navigate to="/" replace />;
  }

  return <>{children}</>;
};

// Componente principal da aplicação
const App: React.FC = () => {
  const { isOnline, connectionStatus, setOnlineStatus, setConnectionStatus } =
    useAppStore();

  // Inicializar autenticação
  useAuthInit();

  useEffect(() => {
    // Inicializar store
    initializeStore();

    // Monitorar status de conexão WebSocket
    const unsubscribe = wsService.onConnectionStatusChange((status) => {
      setConnectionStatus(status);
    });

    // Monitorar status online/offline
    const handleOnline = () => setOnlineStatus(true);
    const handleOffline = () => setOnlineStatus(false);

    window.addEventListener("online", handleOnline);
    window.addEventListener("offline", handleOffline);

    // Cleanup
    return () => {
      unsubscribe();
      window.removeEventListener("online", handleOnline);
      window.removeEventListener("offline", handleOffline);
    };
  }, [setOnlineStatus, setConnectionStatus]);

  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
          {/* Rotas principais */}
          <Routes>
            {/* Página inicial */}
            <Route path="/" element={<NewHomePage />} />

            {/* Dashboard do restaurante (admin do restaurante) - ESPECÍFICO PRIMEIRO */}
            <Route
              path="/restaurant/:restaurantId/dashboard/*"
              element={
                <ProtectedRestaurantRoute>
                  <RestaurantDashboard />
                </ProtectedRestaurantRoute>
              }
            />

            {/* Rota de fallback para compatibilidade - removida para evitar redirecionamento forçado */}

            {/* Rota de teste */}
            <Route
              path="/test"
              element={
                <div style={{ padding: "20px", fontSize: "24px" }}>
                  Teste de Rota Funcionando!
                </div>
              }
            />

            {/* Página do restaurante para clientes - ROTA ESPECÍFICA */}
            <Route
              path="/restaurant/public/:restaurantId"
              element={<RestaurantPage />}
            />

            {/* Admin Principal - Acesso direto */}
            <Route path="/admin/login" element={<AdminLoginPage />} />
            <Route
              path="/admin/*"
              element={
                <ProtectedAdminRoute>
                  <AdminDashboard />
                </ProtectedAdminRoute>
              }
            />

            {/* Login do restaurante (header) */}
            <Route path="/login" element={<AdminLogin />} />

            {/* Analytics Dashboard */}
            <Route path="/analytics" element={<AnalyticsDashboard />} />

            {/* Interface do Cliente (via QR Code) */}
            <Route path="/client/:restaurantId" element={<ClientInterface />} />

            {/* Página 404 */}
            <Route path="*" element={<NotFoundPage />} />
          </Routes>

          {/* Container de notificações */}
          <NotificationContainer />

          {/* Toast notifications */}
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: "var(--toast-bg)",
                color: "var(--toast-color)",
                border: "1px solid var(--toast-border)",
              },
              success: {
                iconTheme: {
                  primary: "#10B981",
                  secondary: "#FFFFFF",
                },
              },
              error: {
                iconTheme: {
                  primary: "#EF4444",
                  secondary: "#FFFFFF",
                },
              },
            }}
          />
        </div>
      </Router>

      {/* React Query DevTools (apenas em desenvolvimento) */}
      {import.meta.env.DEV && <ReactQueryDevtools initialIsOpen={false} />}
    </QueryClientProvider>
  );
};

export default App;
